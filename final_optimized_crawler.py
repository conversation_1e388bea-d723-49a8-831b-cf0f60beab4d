#!/usr/bin/env python3
"""
最终优化版爬虫
集成所有优化技术，专门处理BOSS直聘的滑块验证
"""

import asyncio
import time
import random
import os
from typing import List, Tuple, Optional
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

class FinalOptimizedCrawler:
    """最终优化版爬虫"""
    
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        self.success_count = 0
        self.total_count = 0
        self.discovered_urls = set()
        
        # 确保输出目录存在
        os.makedirs("output", exist_ok=True)
        os.makedirs("debug", exist_ok=True)
    
    async def crawl_with_slider_handling(self, url: str, max_retries: int = 3) -> Tuple[bool, str, List[str]]:
        """带滑块处理的爬取方法"""
        for attempt in range(max_retries):
            try:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}: {url}")
                
                # 创建浏览器配置
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium",
                    viewport_width=1920,
                    viewport_height=1080,
                    user_agent=random.choice(self.user_agents),
                    extra_args=[
                        "--disable-blink-features=AutomationControlled",
                        "--disable-dev-shm-usage",
                        "--no-sandbox"
                    ]
                )
                
                crawler_config = CrawlerRunConfig(
                    wait_until="domcontentloaded",
                    page_timeout=30000,
                    delay_before_return_html=3.0
                )
                
                async with AsyncWebCrawler(config=browser_config) as crawler:
                    # 步骤1: 先访问首页建立会话
                    print("  📍 建立会话...")
                    home_result = await crawler.arun(
                        url="https://www.zhipin.com",
                        config=crawler_config
                    )
                    
                    if not home_result.success:
                        print(f"  ❌ 首页访问失败")
                        continue
                    
                    # 等待
                    await asyncio.sleep(random.uniform(2, 4))
                    
                    # 步骤2: 访问目标页面
                    print("  🎯 访问目标页面...")
                    result = await crawler.arun(url=url, config=crawler_config)
                    
                    if not result.success:
                        print(f"  ❌ 目标页面访问失败")
                        continue
                    
                    # 步骤3: 检查页面类型
                    page_type = self._analyze_page_type(result.html)
                    
                    if page_type == "slider_captcha":
                        print("  🧩 检测到滑块验证，尝试处理...")
                        
                        # 尝试自动处理滑块
                        success, final_html = await self._handle_slider_captcha(crawler, url)
                        
                        if success:
                            urls = self._extract_job_urls(final_html)
                            return True, final_html, urls
                        else:
                            print(f"  ❌ 滑块验证处理失败")
                            continue
                    
                    elif page_type == "job_list":
                        print("  ✅ 获取到职位列表页面")
                        urls = self._extract_job_urls(result.html)
                        return True, result.html, urls
                    
                    elif page_type == "security_check":
                        print("  ⚠️ 检测到其他安全检查")
                        # 等待一段时间后重试
                        await asyncio.sleep(random.uniform(5, 10))
                        continue
                    
                    else:
                        print(f"  ❓ 未知页面类型，长度: {len(result.html)}")
                        # 保存调试页面
                        debug_file = f"debug/unknown_page_{int(time.time())}.html"
                        with open(debug_file, "w", encoding="utf-8") as f:
                            f.write(result.html)
                        print(f"  📄 调试页面已保存: {debug_file}")
                        continue
                        
            except Exception as e:
                print(f"  ❌ 爬取异常: {str(e)}")
                continue
        
        return False, "", []
    
    async def _handle_slider_captcha(self, crawler, url: str) -> Tuple[bool, str]:
        """处理滑块验证码"""
        try:
            print("    🔧 执行滑块验证处理...")
            
            # 创建带滑块处理的配置
            slider_config = CrawlerRunConfig(
                wait_until="domcontentloaded",
                page_timeout=45000,
                delay_before_return_html=5.0,
                js_code=[
                    """
                    // 滑块验证处理脚本
                    console.log('开始滑块验证处理...');
                    
                    // 等待页面完全加载
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    // 查找滑块元素
                    const sliderSelectors = [
                        '.verify-row-code',
                        '.slider-btn',
                        '.slide-btn',
                        '[class*="slider"]',
                        '[class*="verify"]'
                    ];
                    
                    let slider = null;
                    for (const selector of sliderSelectors) {
                        slider = document.querySelector(selector);
                        if (slider) {
                            console.log('找到滑块元素:', selector);
                            break;
                        }
                    }
                    
                    if (slider) {
                        // 模拟点击滑块区域
                        const rect = slider.getBoundingClientRect();
                        const clickX = rect.left + rect.width / 2;
                        const clickY = rect.top + rect.height / 2;
                        
                        // 触发点击事件
                        slider.click();
                        
                        // 等待可能的页面变化
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        
                        console.log('滑块处理完成');
                    } else {
                        console.log('未找到滑块元素');
                    }
                    
                    // 检查页面是否有变化
                    const hasJobContent = document.querySelector('.job-list, .job-item, [class*="job"]');
                    if (hasJobContent) {
                        console.log('检测到职位内容');
                    }
                    """
                ]
            )
            
            # 重新访问页面并执行滑块处理
            result = await crawler.arun(url=url, config=slider_config)
            
            if result.success and result.html:
                page_type = self._analyze_page_type(result.html)
                
                if page_type == "job_list":
                    print("    ✅ 滑块验证成功")
                    return True, result.html
                else:
                    print("    ❌ 滑块验证未成功")
                    return False, result.html
            else:
                return False, ""
                
        except Exception as e:
            print(f"    ❌ 滑块处理异常: {str(e)}")
            return False, ""
    
    def _analyze_page_type(self, html_content: str) -> str:
        """分析页面类型"""
        if not html_content:
            return "empty"
        
        # 滑块验证页面
        slider_indicators = [
            "网站访客身份验证",
            "wrap-verify-slider", 
            "page-verify-slider",
            "verify-row-code"
        ]
        
        for indicator in slider_indicators:
            if indicator in html_content:
                return "slider_captcha"
        
        # 职位列表页面
        job_indicators = [
            "job-list",
            "job-item",
            "职位名称",
            "薪资范围",
            "工作经验"
        ]
        
        job_count = 0
        for indicator in job_indicators:
            if indicator in html_content:
                job_count += 1
        
        if job_count >= 2:
            return "job_list"
        
        # 其他安全检查
        security_indicators = [
            "请稍候",
            "正在加载中",
            "安全验证",
            "异常访问行为"
        ]
        
        for indicator in security_indicators:
            if indicator in html_content:
                return "security_check"
        
        # 页面太短可能是错误页面
        if len(html_content) < 5000:
            return "error_page"
        
        return "unknown"
    
    def _extract_job_urls(self, html_content: str) -> List[str]:
        """提取职位URL"""
        import re
        
        # 匹配BOSS直聘职位URL的正则表达式
        url_patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html',
            r'/job_detail/[a-zA-Z0-9]+\.html',
            r'href="([^"]*job_detail[^"]*)"',
            r'href="([^"]*position[^"]*)"'
        ]
        
        urls = set()
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/'):
                    url = f"https://www.zhipin.com{match}"
                elif match.startswith('http'):
                    url = match
                else:
                    continue
                
                urls.add(url)
        
        return list(urls)
    
    async def crawl_city_positions(self, city_code: str, city_name: str, max_pages: int = 5) -> List[str]:
        """爬取指定城市的职位"""
        print(f"\n🏙️ 开始爬取城市: {city_name} (代码: {city_code})")
        
        all_urls = []
        position_codes = ["100000", "110000", "120000"]  # 技术、产品、设计
        
        for position_code in position_codes:
            print(f"  📋 爬取职位分类: {position_code}")
            
            for page in range(1, max_pages + 1):
                url = f"https://www.zhipin.com/web/geek/jobs?city={city_code}&position={position_code}&page={page}"
                
                success, html, urls = await self.crawl_with_slider_handling(url)
                
                self.total_count += 1
                
                if success:
                    self.success_count += 1
                    all_urls.extend(urls)
                    
                    print(f"    ✅ 第{page}页成功，发现{len(urls)}个URL")
                    
                    # 实时保存URL
                    if urls:
                        self._save_urls_realtime(urls, city_name)
                else:
                    print(f"    ❌ 第{page}页失败")
                
                # 页面间延时
                await asyncio.sleep(random.uniform(3, 6))
            
            # 职位分类间延时
            await asyncio.sleep(random.uniform(5, 8))
        
        return all_urls
    
    def _save_urls_realtime(self, urls: List[str], city_name: str):
        """实时保存URL"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"output/boss_urls_{city_name}_{timestamp}.txt"
        
        with open(filename, "a", encoding="utf-8") as f:
            for url in urls:
                if url not in self.discovered_urls:
                    f.write(url + "\n")
                    self.discovered_urls.add(url)
        
        print(f"    💾 已保存{len(urls)}个新URL到 {filename}")
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "="*60)
        print("📊 爬取统计")
        print("="*60)
        print(f"总请求数: {self.total_count}")
        print(f"成功请求数: {self.success_count}")
        print(f"成功率: {self.success_count/self.total_count*100:.1f}%" if self.total_count > 0 else "成功率: 0%")
        print(f"发现URL总数: {len(self.discovered_urls)}")

async def main():
    """主函数"""
    print("🚀 最终优化版BOSS直聘爬虫")
    print("="*60)
    print("✅ 集成滑块验证处理")
    print("✅ 智能页面类型识别")
    print("✅ 多重访问策略")
    print("✅ 实时URL保存")
    print("="*60)
    
    crawler = FinalOptimizedCrawler()
    
    # 测试爬取北京的职位
    city_code = "101010100"
    city_name = "北京"
    max_pages = 3  # 限制页数进行测试
    
    try:
        urls = await crawler.crawl_city_positions(city_code, city_name, max_pages)
        
        crawler.print_statistics()
        
        if urls:
            print(f"\n✅ 爬取完成，共发现 {len(urls)} 个职位URL")
            print("📁 URL已保存到 output/ 目录")
        else:
            print("\n⚠️ 未发现任何URL，可能需要:")
            print("  1. 配置代理IP")
            print("  2. 手动处理滑块验证")
            print("  3. 调整爬取策略")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断爬取")
        crawler.print_statistics()
    except Exception as e:
        print(f"\n❌ 爬取异常: {str(e)}")
        crawler.print_statistics()

if __name__ == "__main__":
    asyncio.run(main())
