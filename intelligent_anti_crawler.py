"""
智能反爬虫绕过器
集成多种反爬虫绕过技术，包括验证码识别、行为模拟等
"""

import asyncio
import random
import time
import json
import base64
from typing import Dict, List, Optional, Tuple
import aiohttp
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from config import ANTI_CRAWLER_CONFIG
from advanced_proxy_manager import proxy_manager

class IntelligentAntiCrawler:
    """智能反爬虫绕过器"""
    
    def __init__(self):
        self.session_cookies = {}
        self.request_history = []
        self.last_request_time = 0
        
    async def create_stealth_browser_config(self) -> BrowserConfig:
        """创建隐身浏览器配置"""
        # 随机化浏览器指纹
        viewport_sizes = [
            (1920, 1080), (1366, 768), (1440, 900), 
            (1536, 864), (1280, 720), (1600, 900)
        ]
        
        user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]
        
        viewport = random.choice(viewport_sizes)
        user_agent = random.choice(user_agents)
        
        # 获取代理
        proxy_url = await proxy_manager.get_next_proxy()
        
        config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=viewport[0],
            viewport_height=viewport[1],
            user_agent=user_agent,
            proxy=proxy_url,
            extra_args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        )
        
        return config
    
    async def create_stealth_crawler_config(self) -> CrawlerRunConfig:
        """创建隐身爬虫配置"""
        # 随机化请求头
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "max-age=0"
        }
        
        # 随机添加一些可选头
        optional_headers = {
            "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"'
        }
        
        if random.random() > 0.5:
            headers.update(optional_headers)
        
        config = CrawlerRunConfig(
            wait_until="domcontentloaded",
            page_timeout=30000,
            delay_before_return_html=random.uniform(1.0, 3.0),
            js_code=[
                # 隐藏webdriver特征
                """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                """,
                # 随机化canvas指纹
                """
                const getContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type) {
                    if (type === '2d') {
                        const context = getContext.call(this, type);
                        const getImageData = context.getImageData;
                        context.getImageData = function(x, y, w, h) {
                            const imageData = getImageData.call(this, x, y, w, h);
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                                imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                            }
                            return imageData;
                        };
                        return context;
                    }
                    return getContext.call(this, type);
                };
                """
            ]
        )
        
        return config
    
    async def simulate_human_behavior(self, page_content: str) -> bool:
        """模拟人类浏览行为"""
        if not ANTI_CRAWLER_CONFIG.get("simulate_human_behavior", True):
            return True
        
        # 模拟阅读时间
        reading_time = len(page_content) / 1000 * random.uniform(0.5, 2.0)
        reading_time = min(reading_time, 10.0)  # 最多10秒
        
        print(f"🤖 模拟人类阅读行为，等待 {reading_time:.1f} 秒...")
        await asyncio.sleep(reading_time)
        
        return True
    
    async def handle_rate_limiting(self):
        """处理频率限制"""
        now = time.time()
        min_interval = ANTI_CRAWLER_CONFIG.get("request_delay_range", (3.0, 8.0))[0]
        
        if self.last_request_time > 0:
            elapsed = now - self.last_request_time
            if elapsed < min_interval:
                wait_time = min_interval - elapsed + random.uniform(0, 2)
                print(f"⏱️ 频率控制，等待 {wait_time:.1f} 秒...")
                await asyncio.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    async def detect_security_check(self, html_content: str) -> Tuple[bool, str]:
        """检测安全检查页面"""
        security_indicators = [
            "请稍候",
            "正在加载中",
            "安全验证",
            "异常访问行为",
            "完成验证后即可正常使用",
            "滑块验证",
            "点击验证",
            "人机验证"
        ]
        
        for indicator in security_indicators:
            if indicator in html_content:
                return True, indicator
        
        # 检查页面长度（安全检查页面通常很短）
        if len(html_content) < 5000:
            return True, "页面内容过短"
        
        return False, ""
    
    async def solve_captcha(self, captcha_type: str, captcha_data: str) -> Optional[str]:
        """解决验证码"""
        if not ANTI_CRAWLER_CONFIG.get("auto_captcha_solving", True):
            return None
        
        service = ANTI_CRAWLER_CONFIG.get("captcha_service", "2captcha")
        api_key = ANTI_CRAWLER_CONFIG.get("captcha_api_key", "")
        
        if not api_key:
            print("❌ 未配置验证码服务API密钥")
            return None
        
        try:
            if service == "2captcha":
                return await self._solve_with_2captcha(captcha_type, captcha_data, api_key)
            elif service == "anticaptcha":
                return await self._solve_with_anticaptcha(captcha_type, captcha_data, api_key)
        except Exception as e:
            print(f"❌ 验证码识别失败: {str(e)}")
        
        return None
    
    async def _solve_with_2captcha(self, captcha_type: str, captcha_data: str, api_key: str) -> Optional[str]:
        """使用2captcha服务解决验证码"""
        # 这里需要根据具体的验证码类型实现
        # 示例代码，需要根据实际API调整
        
        async with aiohttp.ClientSession() as session:
            # 提交验证码
            submit_data = {
                "key": api_key,
                "method": "base64",
                "body": captcha_data
            }
            
            async with session.post("http://2captcha.com/in.php", data=submit_data) as response:
                result = await response.text()
                if result.startswith("OK|"):
                    captcha_id = result.split("|")[1]
                else:
                    return None
            
            # 等待结果
            for _ in range(30):  # 最多等待30次
                await asyncio.sleep(5)
                
                async with session.get(f"http://2captcha.com/res.php?key={api_key}&action=get&id={captcha_id}") as response:
                    result = await response.text()
                    if result.startswith("OK|"):
                        return result.split("|")[1]
                    elif result != "CAPCHA_NOT_READY":
                        break
        
        return None
    
    async def _solve_with_anticaptcha(self, captcha_type: str, captcha_data: str, api_key: str) -> Optional[str]:
        """使用anticaptcha服务解决验证码"""
        # 类似的实现
        pass
    
    async def intelligent_crawl(self, url: str) -> Tuple[bool, str, str]:
        """智能爬取，自动处理反爬虫机制"""
        try:
            # 频率控制
            await self.handle_rate_limiting()
            
            # 创建隐身配置
            browser_config = await self.create_stealth_browser_config()
            crawler_config = await self.create_stealth_crawler_config()
            
            print(f"🕵️ 智能爬取: {url}")
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)
                
                if result.success and result.html:
                    # 检测安全检查
                    is_security_check, reason = await self.detect_security_check(result.html)
                    
                    if is_security_check:
                        print(f"🚨 检测到安全检查: {reason}")
                        
                        # 尝试处理安全检查
                        handled_html = await self.handle_security_check(result.html, url)
                        if handled_html:
                            await self.simulate_human_behavior(handled_html)
                            return True, handled_html, "安全检查已处理"
                        else:
                            # 标记代理可能被封
                            proxy_url = browser_config.proxy
                            if proxy_url:
                                await proxy_manager.mark_proxy_failure(proxy_url, is_banned=True)
                            return False, result.html, f"安全检查处理失败: {reason}"
                    else:
                        # 正常页面
                        await self.simulate_human_behavior(result.html)
                        
                        # 标记代理成功
                        proxy_url = browser_config.proxy
                        if proxy_url:
                            await proxy_manager.mark_proxy_success(proxy_url)
                        
                        return True, result.html, "爬取成功"
                else:
                    error_msg = result.error_message if hasattr(result, 'error_message') else 'Unknown error'
                    return False, "", f"爬取失败: {error_msg}"
                    
        except Exception as e:
            return False, "", f"智能爬取异常: {str(e)}"
    
    async def handle_security_check(self, html_content: str, url: str) -> Optional[str]:
        """处理安全检查页面"""
        print("🔧 尝试处理安全检查页面...")
        
        # 等待一段时间，让页面自动跳转
        await asyncio.sleep(random.uniform(5, 10))
        
        # 可以在这里添加更复杂的处理逻辑
        # 比如识别验证码、模拟点击等
        
        return None

# 全局智能反爬虫绕过器实例
anti_crawler = IntelligentAntiCrawler()
