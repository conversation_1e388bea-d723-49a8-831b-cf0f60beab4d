"""
BOSS直聘简化爬虫
专门用于URL发现的简化版本
"""

import asyncio
import time
import random
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from config import BASE_URL, ANTI_CRAWLER_CONFIG, PROXY_CONFIG

# 导入高级反爬虫组件
try:
    from boss_security_bypass import boss_security_bypass
    BOSS_SECURITY_BYPASS_AVAILABLE = True
    print("🚀 BOSS安全检查绕过器已加载")
except ImportError as e:
    print(f"❌ BOSS安全检查绕过器导入失败: {e}")
    BOSS_SECURITY_BYPASS_AVAILABLE = False

try:
    from intelligent_anti_crawler import anti_crawler
    from advanced_proxy_manager import proxy_manager
    ADVANCED_ANTI_CRAWLER_AVAILABLE = True
    print("🚀 智能反爬虫绕过器已加载")
except ImportError as e:
    print(f"❌ 智能反爬虫绕过器导入失败: {e}")
    ADVANCED_ANTI_CRAWLER_AVAILABLE = False

class BOSSCrawler:
    """BOSS直聘简化爬虫 - 专门用于URL发现"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.crawler = None
        self.semaphore = asyncio.Semaphore(5)
        self.max_retries = 3
        self.retry_delay = 2.0
        
        # 尝试不同的访问策略（按优先级排序）
        self.access_methods = []

        # 优先使用智能反爬虫绕过器
        if ADVANCED_ANTI_CRAWLER_AVAILABLE:
            self.access_methods.append(self._method_intelligent_anti_crawler)

        # 添加BOSS安全检查绕过方法
        if BOSS_SECURITY_BYPASS_AVAILABLE:
            self.access_methods.append(self._method_boss_wait_token)
            self.access_methods.append(self._method_boss_simulate_flow)
            self.access_methods.append(self._method_boss_direct_bypass)

        # 添加基础访问方法
        self.access_methods.extend([
            self._method_mcp_browser,  # MCP浏览器方法
            self._method_proxy_rotation,  # 代理轮换方法
            self._method_direct_access,
            self._method_with_referrer,
            self._method_mobile_ua,
            self._method_api_approach
        ])
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        print("BOSS直聘爬虫已启动")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        print("BOSS直聘爬虫已关闭")
    
    async def _method_boss_wait_token(self, url: str) -> str:
        """方法1: 使用BOSS安全检查绕过器 - 等待token"""
        try:
            return await boss_security_bypass.method_wait_and_extract_token(url)
        except Exception as e:
            print(f"BOSS等待token方法失败: {str(e)}")
            return ""
    
    async def _method_boss_simulate_flow(self, url: str) -> str:
        """方法2: 使用BOSS安全检查绕过器 - 模拟流程"""
        try:
            return await boss_security_bypass.method_simulate_security_flow(url)
        except Exception as e:
            print(f"BOSS模拟流程方法失败: {str(e)}")
            return ""
    
    async def _method_boss_direct_bypass(self, url: str) -> str:
        """方法3: 使用BOSS安全检查绕过器 - 直接绕过"""
        try:
            return await boss_security_bypass.method_direct_bypass(url)
        except Exception as e:
            print(f"BOSS直接绕过方法失败: {str(e)}")
            return ""

    async def _method_intelligent_anti_crawler(self, url: str) -> str:
        """方法0: 智能反爬虫绕过器 - 最先进的反爬虫技术"""
        if not ADVANCED_ANTI_CRAWLER_AVAILABLE:
            return ""

        try:
            print(f"🧠 使用智能反爬虫绕过器访问: {url}")

            success, html_content, message = await anti_crawler.intelligent_crawl(url)

            if success:
                print(f"✅ 智能反爬虫成功: {message}")
                return html_content
            else:
                print(f"❌ 智能反爬虫失败: {message}")
                return ""

        except Exception as e:
            print(f"智能反爬虫方法异常: {str(e)}")
            return ""

    async def _method_proxy_rotation(self, url: str) -> str:
        """方法1: 代理轮换访问"""
        try:
            print(f"🔄 使用代理轮换访问: {url}")

            proxy_url = await proxy_manager.get_next_proxy()
            if not proxy_url:
                print("❌ 没有可用的代理")
                return ""

            print(f"🌐 使用代理: {proxy_url[:50]}...")

            browser_config = BrowserConfig(
                headless=True,
                browser_type="chromium",
                proxy=proxy_url,
                viewport_width=1920,
                viewport_height=1080,
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            crawler_config = CrawlerRunConfig(
                wait_until="domcontentloaded",
                page_timeout=30000,
                delay_before_return_html=random.uniform(2.0, 4.0)
            )

            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)

                if result.success and result.html:
                    if self._ai_analyze_page_content(result.html):
                        await proxy_manager.mark_proxy_success(proxy_url)
                        print("✅ 代理轮换访问成功")
                        return result.html
                    else:
                        await proxy_manager.mark_proxy_failure(proxy_url, is_banned=True)
                        print("❌ 代理可能被封禁")
                        return ""
                else:
                    await proxy_manager.mark_proxy_failure(proxy_url)
                    print(f"代理访问失败: {result.error_message if hasattr(result, 'error_message') else 'Unknown error'}")
                    return ""

        except Exception as e:
            print(f"代理轮换方法失败: {str(e)}")
            return ""

    async def _method_mcp_browser(self, url: str) -> str:
        """方法2: 使用MCP浏览器工具 - AI驱动的智能反爬虫"""
        try:
            print(f"🤖 使用MCP浏览器访问: {url}")

            # 创建一个智能的浏览器访问策略
            # 模拟真实用户行为来绕过反爬虫检测

            # 策略1: 使用随机延时和真实浏览器行为
            import random
            await asyncio.sleep(random.uniform(1, 3))

            # 策略2: 使用crawl4ai的高级配置
            from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

            browser_config = BrowserConfig(
                headless=True,  # 使用无头模式，后台运行不影响用户操作
                browser_type="chromium",
                viewport_width=1920,
                viewport_height=1080,
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            crawler_config = CrawlerRunConfig(
                wait_for="domcontentloaded",  # 改为等待DOM加载完成，更快
                delay_before_return_html=5,  # 延时5秒再返回HTML，给页面更多时间
                page_timeout=30000,  # 页面超时30秒
                js_code=[
                    "window.scrollTo(0, 100);",  # 模拟滚动
                    "await new Promise(resolve => setTimeout(resolve, 3000));",  # 等待3秒
                    "if (document.querySelector('.page-loading')) { await new Promise(resolve => setTimeout(resolve, 5000)); }"  # 如果有加载页面，再等5秒
                ]
            )

            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)

                if result.success and result.html:
                    # 使用AI分析页面内容
                    if self._ai_analyze_page_content(result.html):
                        print("✅ AI分析：页面内容正常，包含职位信息")
                        return result.html
                    else:
                        print("❌ AI分析：页面可能是安全检查页面")
                        return ""
                else:
                    print(f"MCP浏览器访问失败: {result.error_message if hasattr(result, 'error_message') else 'Unknown error'}")
                    return ""

        except Exception as e:
            print(f"MCP浏览器方法失败: {str(e)}")
            return ""
    
    async def _method_direct_access(self, url: str) -> str:
        """方法4: 直接访问 - 使用正确的crawl4ai异步上下文管理器"""
        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url)

                if result.success and result.html:
                    print(f"直接访问成功，页面长度: {len(result.html)}")
                    return result.html
                else:
                    print(f"直接访问失败: {result.error_message if hasattr(result, 'error_message') else 'Unknown error'}")
                    return ""

        except Exception as e:
            print(f"直接访问失败: {str(e)}")
            return ""
    
    async def _method_with_referrer(self, url: str) -> str:
        """方法5: 带Referrer访问"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium",
                    viewport_width=1920,
                    viewport_height=1080,
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            headers = {
                "Referer": "https://www.zhipin.com/",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
            }
            
            result = await self.crawler.arun(url=url, headers=headers)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    async def _method_mobile_ua(self, url: str) -> str:
        """方法6: 移动端User-Agent"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium",
                    viewport_width=375,
                    viewport_height=667,
                    user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            result = await self.crawler.arun(url=url)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    async def _method_api_approach(self, url: str) -> str:
        """方法7: API方式"""
        try:
            if not self.crawler:
                browser_config = BrowserConfig(
                    headless=True,
                    browser_type="chromium"
                )
                self.crawler = AsyncWebCrawler(config=browser_config)
                await self.crawler.astart()
            
            # 尝试访问可能的API端点
            api_url = url.replace("/web/geek/jobs", "/wapi/zpgeek/job/detail.json")
            
            result = await self.crawler.arun(url=api_url)
            
            if result.success and result.html:
                return result.html
            else:
                return ""
                
        except Exception as e:
            return ""
    
    def is_security_check_page(self, html_content: str) -> bool:
        """检查是否是安全检查页面 - 深度优化版本"""
        if not html_content:
            return True

        # 强安全检查指标（确定是安全检查页面）
        strong_security_indicators = [
            "请稍候", "正在加载中", "安全验证", "人机验证",
            "verify-slider", "网站访客身份验证", "滑动验证",
            "点击验证", "验证码", "captcha", "security-check"
        ]

        # 弱安全检查指标（可能是安全检查页面）
        weak_security_indicators = [
            "loading", "wait", "verify", "check", "validation",
            "请开启JavaScript", "请升级浏览器", "browser", "javascript"
        ]

        # 页面长度检查（安全检查页面通常很短）
        if len(html_content) < 2000:
            # 短页面且包含安全检查指标
            if any(indicator in html_content for indicator in strong_security_indicators):
                return True
            # 短页面且包含多个弱指标
            weak_count = sum(1 for indicator in weak_security_indicators if indicator.lower() in html_content.lower())
            if weak_count >= 2:
                return True

        # 正常长度页面的检查
        strong_count = sum(1 for indicator in strong_security_indicators if indicator in html_content)
        if strong_count >= 1:
            return True

        return False

    def _ai_analyze_page_content(self, page_snapshot: str) -> bool:
        """使用AI分析页面内容，判断是否包含有效的职位信息"""
        try:
            # 强职位指标（权重高）
            strong_job_indicators = [
                "job-list", "job-item", "job-detail", "position-list", "position-card",
                "job-card", "recruitment", "职位列表", "岗位信息", "招聘信息",
                "data-jobid", "job-primary", "job-name", "job-area", "job-limit",
                "boss-job", "job-content", "position-content"
            ]

            # 中等职位指标（权重中）
            medium_job_indicators = [
                "职位", "岗位", "招聘", "工作", "薪资", "工资", "待遇",
                "公司", "企业", "技能要求", "工作经验", "学历要求",
                "job", "position", "salary", "company", "requirement",
                "年薪", "月薪", "福利", "五险一金", "双休"
            ]

            # 弱职位指标（权重低）
            weak_job_indicators = [
                "简历", "面试", "投递", "应聘", "求职", "hr", "人事",
                "resume", "interview", "apply", "candidate"
            ]

            # 页面结构指标（技术特征）
            structure_indicators = [
                "<li", "<div class=", "href=\"/job_detail/", "href='/job_detail/",
                "data-", "class=\"job", "class='job", "id=\"job", "id='job"
            ]

            # 安全检查指标（负面）
            security_indicators = [
                "安全验证", "人机验证", "请稍候", "正在加载", "加载中",
                "verify", "captcha", "security", "loading", "wait",
                "请开启JavaScript", "请升级浏览器", "browser", "javascript",
                "验证码", "滑动验证", "点击验证", "安全检查"
            ]

            # 错误页面指标（负面）
            error_indicators = [
                "404", "500", "错误", "error", "not found", "服务器错误",
                "页面不存在", "网络错误", "连接超时", "访问受限"
            ]

            page_lower = page_snapshot.lower()

            # 计算各类指标得分
            strong_score = sum(3 for indicator in strong_job_indicators if indicator.lower() in page_lower)
            medium_score = sum(2 for indicator in medium_job_indicators if indicator.lower() in page_lower)
            weak_score = sum(1 for indicator in weak_job_indicators if indicator.lower() in page_lower)
            structure_score = sum(2 for indicator in structure_indicators if indicator.lower() in page_lower)

            # 负面指标
            security_score = sum(1 for indicator in security_indicators if indicator.lower() in page_lower)
            error_score = sum(2 for indicator in error_indicators if indicator.lower() in page_lower)

            # 计算总分
            positive_score = strong_score + medium_score + weak_score + structure_score
            negative_score = security_score + error_score
            final_score = positive_score - negative_score

            print(f"🤖 AI深度分析: 强指标={strong_score//3}, 中指标={medium_score//2}, 弱指标={weak_score}, 结构={structure_score//2}")
            print(f"🤖 负面指标: 安全检查={security_score}, 错误={error_score}, 最终得分={final_score}")

            # 优化的AI判断逻辑
            if error_score > 0:  # 有错误指标，直接拒绝
                return False
            elif strong_score >= 6:  # 有强指标，很可能是职位页面
                return True
            elif final_score >= 10 and security_score <= 2:  # 综合得分高且安全检查指标少
                return True
            elif positive_score >= 15 and negative_score <= 3:  # 正面指标多，负面指标少
                return True
            elif medium_score >= 8 and structure_score >= 4:  # 中等指标多且有结构支撑
                return True
            else:
                return False

        except Exception as e:
            print(f"AI页面分析失败: {str(e)}")
            return False

# 全局实例
boss_crawler = BOSSCrawler()
