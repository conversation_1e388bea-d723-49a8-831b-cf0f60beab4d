"""
高级代理管理器
支持代理IP轮换、健康检查、智能切换等功能
"""

import asyncio
import random
import time
import aiohttp
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from config import PROXY_CONFIG

class ProxyStatus(Enum):
    ACTIVE = "active"
    FAILED = "failed"
    COOLDOWN = "cooldown"
    BANNED = "banned"

@dataclass
class ProxyInfo:
    url: str
    status: ProxyStatus = ProxyStatus.ACTIVE
    success_count: int = 0
    failure_count: int = 0
    last_used: float = 0
    last_success: float = 0
    response_time: float = 0
    cooldown_until: float = 0
    
    @property
    def success_rate(self) -> float:
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0
    
    @property
    def is_available(self) -> bool:
        now = time.time()
        return (self.status == ProxyStatus.ACTIVE and 
                now > self.cooldown_until)

class AdvancedProxyManager:
    """高级代理管理器"""
    
    def __init__(self):
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        self.request_counts: Dict[str, int] = {}
        self.logger = logging.getLogger(__name__)
        
        # 从配置加载代理
        self._load_proxies_from_config()
    
    def _load_proxies_from_config(self):
        """从配置文件加载代理"""
        proxy_urls = PROXY_CONFIG.get("proxy_pool", [])
        for url in proxy_urls:
            if url.strip():  # 忽略空字符串
                self.proxies.append(ProxyInfo(url=url.strip()))
        
        self.logger.info(f"加载了 {len(self.proxies)} 个代理")
    
    def add_proxy(self, proxy_url: str):
        """添加代理"""
        proxy_info = ProxyInfo(url=proxy_url)
        self.proxies.append(proxy_info)
        self.logger.info(f"添加代理: {proxy_url}")
    
    async def get_next_proxy(self) -> Optional[str]:
        """获取下一个可用代理"""
        if not self.proxies:
            return None
        
        # 如果禁用代理，返回None
        if not PROXY_CONFIG.get("enable_proxy", True):
            return None
        
        # 尝试找到可用的代理
        available_proxies = [p for p in self.proxies if p.is_available]
        
        if not available_proxies:
            self.logger.warning("没有可用的代理，等待冷却...")
            await asyncio.sleep(5)
            return None
        
        # 根据策略选择代理
        strategy = PROXY_CONFIG.get("rotation_strategy", "round_robin")
        
        if strategy == "round_robin":
            proxy = self._round_robin_selection(available_proxies)
        elif strategy == "random":
            proxy = random.choice(available_proxies)
        elif strategy == "weighted":
            proxy = self._weighted_selection(available_proxies)
        else:
            proxy = available_proxies[0]
        
        # 更新使用统计
        proxy.last_used = time.time()
        self.request_counts[proxy.url] = self.request_counts.get(proxy.url, 0) + 1
        
        return proxy.url
    
    def _round_robin_selection(self, available_proxies: List[ProxyInfo]) -> ProxyInfo:
        """轮询选择代理"""
        if self.current_index >= len(available_proxies):
            self.current_index = 0
        
        proxy = available_proxies[self.current_index]
        self.current_index += 1
        return proxy
    
    def _weighted_selection(self, available_proxies: List[ProxyInfo]) -> ProxyInfo:
        """基于成功率的加权选择"""
        weights = []
        for proxy in available_proxies:
            # 基于成功率和响应时间计算权重
            success_weight = proxy.success_rate * 100
            speed_weight = max(0, 100 - proxy.response_time)
            total_weight = success_weight + speed_weight + 10  # 基础权重
            weights.append(total_weight)
        
        return random.choices(available_proxies, weights=weights)[0]
    
    async def test_proxy(self, proxy_url: str) -> bool:
        """测试代理是否可用"""
        try:
            timeout = aiohttp.ClientTimeout(total=PROXY_CONFIG.get("proxy_timeout", 10))
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                
                async with session.get(
                    "https://httpbin.org/ip",
                    proxy=proxy_url
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        # 更新代理信息
                        proxy_info = self._get_proxy_info(proxy_url)
                        if proxy_info:
                            proxy_info.response_time = response_time
                            proxy_info.last_success = time.time()
                        
                        return True
                    
        except Exception as e:
            self.logger.warning(f"代理测试失败 {proxy_url}: {str(e)}")
        
        return False
    
    def _get_proxy_info(self, proxy_url: str) -> Optional[ProxyInfo]:
        """获取代理信息对象"""
        for proxy in self.proxies:
            if proxy.url == proxy_url:
                return proxy
        return None
    
    async def mark_proxy_success(self, proxy_url: str):
        """标记代理成功"""
        proxy_info = self._get_proxy_info(proxy_url)
        if proxy_info:
            proxy_info.success_count += 1
            proxy_info.status = ProxyStatus.ACTIVE
            proxy_info.last_success = time.time()
    
    async def mark_proxy_failure(self, proxy_url: str, is_banned: bool = False):
        """标记代理失败"""
        proxy_info = self._get_proxy_info(proxy_url)
        if proxy_info:
            proxy_info.failure_count += 1
            
            if is_banned:
                proxy_info.status = ProxyStatus.BANNED
                proxy_info.cooldown_until = time.time() + PROXY_CONFIG.get("proxy_cooldown", 300)
                self.logger.warning(f"代理被封禁: {proxy_url}")
            else:
                proxy_info.status = ProxyStatus.FAILED
                proxy_info.cooldown_until = time.time() + 60  # 短期冷却
    
    async def health_check(self):
        """代理健康检查"""
        if not PROXY_CONFIG.get("proxy_health_check", True):
            return
        
        self.logger.info("开始代理健康检查...")
        
        for proxy in self.proxies:
            if proxy.status == ProxyStatus.BANNED:
                continue
                
            is_healthy = await self.test_proxy(proxy.url)
            
            if is_healthy:
                proxy.status = ProxyStatus.ACTIVE
                self.logger.info(f"代理健康: {proxy.url}")
            else:
                proxy.status = ProxyStatus.FAILED
                proxy.cooldown_until = time.time() + 120
                self.logger.warning(f"代理不健康: {proxy.url}")
            
            await asyncio.sleep(1)  # 避免过快的健康检查
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        stats = {
            "total_proxies": len(self.proxies),
            "active_proxies": len([p for p in self.proxies if p.status == ProxyStatus.ACTIVE]),
            "failed_proxies": len([p for p in self.proxies if p.status == ProxyStatus.FAILED]),
            "banned_proxies": len([p for p in self.proxies if p.status == ProxyStatus.BANNED]),
            "proxy_details": []
        }
        
        for proxy in self.proxies:
            stats["proxy_details"].append({
                "url": proxy.url[:50] + "..." if len(proxy.url) > 50 else proxy.url,
                "status": proxy.status.value,
                "success_rate": f"{proxy.success_rate:.2%}",
                "response_time": f"{proxy.response_time:.2f}s",
                "requests": self.request_counts.get(proxy.url, 0)
            })
        
        return stats

# 全局代理管理器实例
proxy_manager = AdvancedProxyManager()
